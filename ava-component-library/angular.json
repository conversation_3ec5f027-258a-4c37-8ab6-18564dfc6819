{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"play-comp-library": {"projectType": "library", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": true, "flat": false, "export": true, "changeDetection": "OnPush"}, "@schematics/angular:directive": {"standalone": true}, "@schematics/angular:pipe": {"standalone": true}}, "root": "projects/play-comp-library", "sourceRoot": "projects/play-comp-library/src", "prefix": "ava", "architect": {"build": {"builder": "@angular/build:ng-packagr", "options": {"project": "projects/play-comp-library/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/play-comp-library/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/play-comp-library/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": [], "styles": [], "scripts": []}}}}, "playground": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/playground", "sourceRoot": "projects/playground/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"loader": {".ttf": "binary"}, "outputPath": "dist/playground", "index": "projects/playground/src/index.html", "browser": "projects/playground/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/playground/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/playground/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "projects/playground/public", "output": "/assets/"}, {"glob": "**/*", "input": "projects/play-comp-library/src/lib/assets", "output": "/assets/"}], "styles": ["projects/playground/src/styles.scss", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css", "node_modules/animate.css/animate.min.css"], "scripts": [], "baseHref": "/", "deployUrl": "/"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10MB", "maximumError": "10MB"}, {"type": "anyComponentStyle", "maximumWarning": "500kB", "maximumError": "500kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "playground:build:production"}, "development": {"buildTarget": "playground:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": [], "styles": ["projects/playground/src/styles.scss", "node_modules/animate.css/animate.min.css"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "13e5b5c7-a102-4805-bf66-63364eb1c2f1", "schematicCollections": ["angular-eslint"]}}