import { CommonModule, NgFor, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, ElementRef, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChildren, ViewEncapsulation } from '@angular/core';

import { AvaHeaderCellDefDirective } from './directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from './directive/ava-cell-def.directive';
import { AvaColumnDefDirective } from './directive/ava-column-def.directive';
import { IconComponent } from '../icon/icon.component';
import { AvaTextboxComponent } from '../textbox/ava-textbox.component';
import { ButtonComponent } from '../button/button.component';
import { SelectComponent } from '../select/select.component';
import { SelectOptionComponent } from '../select/select-option/select-option.component';




export interface FilterCondition {
  label: string;
  value: string;
}
@Component({
  selector: 'ava-data-grid',
  imports: [CommonModule, AvaTextboxComponent, SelectComponent, SelectOptionComponent,
    ButtonComponent, NgTemplateOutlet, NgFor, IconComponent
  ],
  templateUrl: './data-grid.component.html',
  styleUrl: './data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None

})

export class DataGridComponent implements OnInit {
  @Input() dataSource: any[] = [];
  @Input() displayedColumns: string[] = [];
  @ViewChildren('textboxRef') textboxes!: QueryList<AvaTextboxComponent>;

  @ContentChildren(AvaColumnDefDirective) columnDefs!: QueryList<AvaColumnDefDirective>;
  columns: AvaColumnDefDirective[] = [];
  //sorting 
  sortColumn: string | null = null;
  sortDirection: 'asc' | 'desc' | '' = '';
  sortedData: any[] = [];
  filterColumn: { column: string; type: string; value: any, open: boolean }[] = [];
  defaultFilterConditions: FilterCondition[] = [
    { label: 'Starts With', value: 'Starts With' },
    { label: 'Ends With', value: 'Ends With' },
    { label: 'Contains', value: 'Contains' },
    { label: 'Equal', value: 'Equal' },
    { label: 'Empty', value: 'Empty' },
    { label: 'Does Not Start With', value: 'Does Not Start With' },
    { label: 'Does Not End With', value: 'Does Not End With' },
    { label: 'Does Not Contain', value: 'Does Not Contain' },
    { label: 'Not Equal', value: 'Not Equal' },
    { label: 'Not Empty', value: 'Not Empty' },
  ];
  @Output() dataSorted = new EventEmitter<any[]>();
  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this.sortedData = [...this.dataSource]
  }
  ngAfterContentInit() {
    this.columns = this.displayedColumns
      .map(colName => {
        const col = this.columnDefs.find(col => col.name === colName);
        return col;
      })
      .filter((col): col is AvaColumnDefDirective => !!col);
  }

  get debugColumns() {
    return this.columns.map(col => ({
      name: col.name,
      hasHeader: !!col.headerCellDef,
      hasCell: !!col.cellDef
    }));
  }


  onSort(column: AvaColumnDefDirective) {
    if (!column.sortable) return;

    const columnName = column.name;
    if (this.sortColumn !== columnName) {
      this.sortColumn = columnName;
      this.sortDirection = 'asc';
    } else {
      switch (this.sortDirection) {
        case 'asc':
          this.sortDirection = 'desc';
          break;
        case 'desc':
          this.sortDirection = 'asc';
          break;
        default:
          this.sortDirection = 'asc';
      }
    }
    this.applySort();
  }


  applySort() {
    if (!this.sortColumn || !this.sortDirection) {
      this.sortedData = [...this.dataSource];
    } else {
      this.sortedData = [...this.dataSource].sort((a, b) => {
        const valueA = a[this.sortColumn!];
        const valueB = b[this.sortColumn!];

        if (valueA == null && valueB == null) return 0;
        if (valueA == null) return -1;
        if (valueB == null) return 1;

        const result =
          typeof valueA === 'string' && typeof valueB === 'string'
            ? valueA.localeCompare(valueB)
            : valueA > valueB ? 1 : valueA < valueB ? -1 : 0;

        return this.sortDirection === 'asc' ? result : -result;
      });
    }

    this.dataSource = this.sortedData;
    this.dataSorted.emit(this.sortedData)
  }

  applyFilter(columnName: string, event: Event) {
    event?.stopPropagation();
    console.log('column', columnName)
    const textboxCmp = this.textboxes.find(cmp =>
      cmp?.mapper === columnName
    );
    const searchText = textboxCmp?.value?.trim().toLowerCase() ?? '';
    this.filterColumn = this.filterColumn.filter(f => f.column !== columnName);
    if (searchText) {
      this.filterColumn.push({
        column: columnName,
        type: 'starts with',
        value: searchText,
        open: false
      });
    }



    this.sortedData = this.dataSource.filter(item => {
      return this.filterColumn.every(f => {
        const cellValue = item[f.column]?.toString().toLowerCase();
        return cellValue?.startsWith(f.value);
      });
    });

    console.log('Filters:', this.filterColumn);

    console.log('Filtered by:', columnName, '→', searchText);
    this.cdr.markForCheck();
  }

  clearFilter(columnName: string, event: any) {
    event?.stopPropagation();
    const existingFilter = this.filterColumn.find(f => f.column === columnName);

    if (existingFilter) {
      existingFilter.open = false;
      existingFilter.value = '';
      existingFilter.type = 'starts with';
    } else {
      this.filterColumn.push({
        column: columnName,
        type: 'starts with',
        value: '',
        open: false
      });
    }
  }


  checkForOpen(columnName: string) {
    const isOpen = this.filterColumn.some(
      f => f.column === columnName && f.open === true
    );

    return isOpen;
  }
  openPanel(columnName: string, event: any) {
    event?.stopPropagation();
    const existing = this.filterColumn.find(f => f.column === columnName);
    this.filterColumn.forEach(f => f.open = false);
    if (existing) {
      existing.open = true;
    } else {
      this.filterColumn.push({
        column: columnName,
        type: '',
        value: '',
        open: true
      });
    }
  }




}
