.ava-data-table-wrapper {
    table-layout: auto;

    .data-table-wrapper {
        position: relative;
        overflow-x: auto;
        width: 100%;
        min-height: 200px;

        .ava-data-table {
            table-layout: auto;
            width: max-content;
            min-width: 100%;
            border-collapse: collapse;
            font-family: var(--grid-font-family-body);
            margin: 1rem 0;
            border: 1px solid var(--table-border);

            color: var(--grid-text-color);

            .cell-wrapper {
                position: relative;

                .filter {
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }

            .filter-wrapper {
                position: absolute;
                z-index: 999;
                background-color: var(--grid-background-color-odd);
                border: 1px solid var(--grid-border);
                border-radius: 8px;
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
                padding: 1rem;
                min-width: 240px;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;

                ava-button {
                    &:first-child {
                        margin-right: 10px;
                    }
                }
            }

            th,
            td {
                padding: .5rem 1rem;
                text-align: left;


            }

            td {
                ava-icon {
                    margin-right: 1rem;
                }
            }

            thead {
                background: var(--grid-background-color-even);
            }

            tbody tr {
                transition: background-color 0.3s ease;

                &:nth-child(odd) {
                    background-color: var(--grid-background-color-odd);
                }

                &:nth-child(even) {
                    background: var(--grid-background-color-even);
                }

                .cell-link {
                    color: inherit;
                    text-decoration: none;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }


        }


    }
}