import { Component, ViewChild, ViewContainerRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastPosition } from '../toast.service';

@Component({
  selector: 'ava-toast-container',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ava-toast-container" [ngClass]="positionClass">
      <ng-container #container></ng-container>
    </div>
  `,
  styles: [`
    /* Toast Container - Uses only _toast.css token variables */
    .ava-toast-container {
      position: var(--toast-container-position);
      z-index: var(--toast-container-z-index);
      pointer-events: none;
      display: flex;
      flex-direction: column;
      gap: var(--toast-container-gap);
      max-width: calc(100vw - 40px);
      width: auto;
    }

    .ava-toast-container.top-left {
      top: var(--toast-container-top);
      left: var(--toast-container-top);
    }

    .ava-toast-container.top-center {
      top: var(--toast-container-top);
      left: 50%;
      transform: translateX(-50%);
    }

    .ava-toast-container.top-right {
      top: var(--toast-container-top);
      right: var(--toast-container-right);
      align-items: flex-end;
    }

    .ava-toast-container.bottom-left {
      bottom: var(--toast-container-top);
      left: var(--toast-container-top);
    }

    .ava-toast-container.bottom-center {
      bottom: var(--toast-container-top);
      left: 50%;
      transform: translateX(-50%);
    }

    .ava-toast-container.bottom-right {
      bottom: var(--toast-container-top);
      right: var(--toast-container-right);
      align-items: flex-end;
    }

    @media (max-width: 768px) {
      .ava-toast-container {
        max-width: calc(100vw - 40px);
        width: calc(100vw - 40px);
        left: var(--toast-container-top) !important;
        right: var(--toast-container-top) !important;
        transform: none !important;
      }
    }

    /* Common Toast Structure - Uses only _toast.css token variables */
    .ava-toast-container .ava-toast {
      pointer-events: auto;
      display: flex;
      align-items: flex-start;
      gap: var(--global-spacing-3);
      padding: var(--toast-padding);
      border-radius: var(--toast-border-radius);
      box-shadow: var(--toast-shadow);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateY(100px);
      opacity: 0;
      max-width: 100%;
      min-width: var(--toast-min-width);
      position: relative;
      overflow: hidden;
      font-family: var(--font-family-body);
    }

    .ava-toast-container .ava-toast.ava-toast-show {
      transform: translateY(0);
      opacity: 1;
    }

    /* Override min-width when custom width is set */
    .ava-toast-container .ava-toast[style*="width"] {
      min-width: unset;
      max-width: calc(100vw - 40px);
    }

    .ava-toast-container .ava-toast-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: var(--global-spacing-1);
    }

    .ava-toast-container .ava-toast-content {
      flex: 1;
      min-width: 0;
    }

    .ava-toast-container .ava-toast-title {
      font: var(--toast-title-font);
      color: var(--toast-title-color);
      font-weight: var(--toast-title-weight);
      line-height: var(--toast-title-line-height);
      margin-bottom: var(--toast-title-margin-bottom);
    }

    .ava-toast-container .ava-toast-message {
      font: var(--toast-message-font);
      color: var(--toast-message-color);
      font-weight: var(--toast-message-weight);
      line-height: var(--toast-message-line-height);
      margin-bottom: var(--toast-message-margin-bottom);
    }

    .ava-toast-container .ava-toast-actions {
      margin-top: var(--global-spacing-2);
    }

    .ava-toast-container .ava-toast-close {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      flex-shrink: 0;
      margin-top: var(--global-spacing-1);
    }

    .ava-toast-container .ava-toast-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 3px;
      background: rgba(255, 255, 255, 0.3);
      width: 100%;
      animation: toast-progress linear;
    }

    @keyframes toast-progress {
      from {
        width: 100%;
      }
      to {
        width: 0%;
      }
    }
  `]
})
export class ToastContainerComponent {
  @ViewChild('container', { read: ViewContainerRef, static: true }) 
  container!: ViewContainerRef;

  positionClass = 'top-right';

  setPosition(position: ToastPosition) {
    this.positionClass = position;
  }
}
