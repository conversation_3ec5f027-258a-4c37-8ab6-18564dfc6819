import { Component, ViewChild, ViewContainerRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastPosition } from '../toast.service';

@Component({
  selector: 'ava-toast-container',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ava-toast-container" [ngClass]="positionClass">
      <ng-container #container></ng-container>
    </div>
  `,
  styles: [`
    .ava-toast-container {
      position: fixed;
      z-index: 10000;
      pointer-events: none;
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-width: calc(100vw - 40px);
      width: auto;
    }

    .ava-toast-container.top-left {
      top: 20px;
      left: 20px;
    }

    .ava-toast-container.top-center {
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .ava-toast-container.top-right {
      top: 20px;
      right: 20px;
      align-items: flex-end;
    }

    .ava-toast-container.bottom-left {
      bottom: 20px;
      left: 20px;
    }

    .ava-toast-container.bottom-center {
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .ava-toast-container.bottom-right {
      bottom: 20px;
      right: 20px;
      align-items: flex-end;
    }

    @media (max-width: 768px) {
      .ava-toast-container.top-left,
      .ava-toast-container.top-center,
      .ava-toast-container.top-right,
      .ava-toast-container.bottom-left,
      .ava-toast-container.bottom-center,
      .ava-toast-container.bottom-right {
        max-width: calc(100vw - 40px);
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
        transform: none;
      }
    }
  `]
})
export class ToastContainerComponent {
  @ViewChild('container', { read: ViewContainerRef, static: true }) 
  container!: ViewContainerRef;

  positionClass = 'top-right';

  setPosition(position: ToastPosition) {
    this.positionClass = position;
  }
}
