@use '../toast-base.scss';

.ava-toast-custom {
  background: var(--toast-background);
  color: var(--color-text-primary);
  border: 1px solid var(--toast-border);

  // Allow custom styling to override defaults
  &[style*="background"] {
    border-color: rgba(255, 255, 255, 0.2);
  }

  // Close button styling for ava-button (custom can have different text colors)
  .ava-toast-close {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  
  }

  // For light backgrounds, use darker hover states
  &:not([style*="background"]) .ava-toast-close {
    &:hover {
      background: var(--color-surface-subtle-hover) !important;
    }

    &:active {
      background: var(--color-surface-subtle-active) !important;
    }
  }
}
