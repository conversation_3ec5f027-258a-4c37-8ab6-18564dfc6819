/* Custom Toast Component - Uses only _toast.css token variables */

.ava-toast-custom {
  background: var(--toast-background);
  color: var(--color-text-primary);
  border: 1px solid var(--toast-border);

  // Allow custom styling to override defaults
  &[style*="background"] {
    border-color: rgba(255, 255, 255, 0.2);
  }
}

// For light backgrounds, use darker hover states
.ava-toast-custom:not([style*="background"]) .ava-toast-close {
  &:hover {
    background: var(--color-surface-subtle-hover) !important;
  }

  &:active {
    background: var(--color-surface-subtle-active) !important;
  }
}
