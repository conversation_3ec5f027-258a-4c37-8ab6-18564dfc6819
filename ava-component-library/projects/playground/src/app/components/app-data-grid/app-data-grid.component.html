<div class="button-demo-page">
    <!-- Header -->
    <div class="demo-header" style="background:#fff">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="box container">
                        <h1>Basic</h1>
                        <ava-data-grid [dataSource]="dataSource"
                            [displayedColumns]="['name', 'age','email', 'country','actions']">

                            <ng-container avaColumnDef="name" [sortable]="true" [filter]="true">
                                <ng-container *avaHeaderCellDef> Name </ng-container>
                                <ng-container *avaCellDef="let row"> {{ row.name }} </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="age" [filter]="true">
                                <ng-container *avaHeaderCellDef> Age </ng-container>
                                <ng-container *avaCellDef="let row"> {{ row.age }} </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="email" [filter]="true">
                                <ng-container *avaHeaderCellDef> Email </ng-container>
                                <ng-container *avaCellDef="let row"> {{ row.email }} </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="country" [filter]="true">
                                <ng-container *avaHeaderCellDef> Country </ng-container>
                                <ng-container *avaCellDef="let row"> {{ row.country }} </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="actions">
                                <ng-container *avaHeaderCellDef> Actions </ng-container>
                                <ng-container *avaCellDef="let row">
                                    <ava-icon iconName="trash" iconSize="15" />
                                    <ava-icon iconName="SquarePen" iconSize="15" />
                                </ng-container>
                            </ng-container>

                        </ava-data-grid>
                    </div>

                    <div class="box">
                        <h1>NgModel</h1>
                        <ava-data-grid [dataSource]="dataSource"
                            [displayedColumns]="['name', 'age','email', 'country','actions']">
                            <ng-container avaColumnDef="name" [sortable]="true">
                                <ng-container *avaHeaderCellDef> Name </ng-container>
                                <ng-container *avaCellDef="let row"> {{ row.name }} </ng-container>
                            </ng-container>

                            <ng-container avaColumnDef="age">
                                <ng-container *avaHeaderCellDef> Age </ng-container>
                                <ng-container *avaCellDef="let row">
                                    <input [(ngModel)]="row.age" />
                                </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="email">
                                <ng-container *avaHeaderCellDef> Email </ng-container>
                                <ng-container *avaCellDef="let row">
                                    <input [(ngModel)]="row.email" />
                                </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="country">
                                <ng-container *avaHeaderCellDef> Country </ng-container>
                                <ng-container *avaCellDef="let row">
                                    <input [(ngModel)]="row.country" />
                                </ng-container>
                            </ng-container>
                        </ava-data-grid>
                        {{dataSource | json}}

                    </div>

                    <div class="box">
                        <h1>Form Control</h1>
                        <ava-data-grid [dataSource]="dataSource"
                            [displayedColumns]="['name', 'age','email', 'country','actions']"
                            (dataSorted)="onDataSorted($event)">
                            <ng-container avaColumnDef="name" [sortable]="true">
                                <ng-container *avaHeaderCellDef> Name </ng-container>
                                <ng-container *avaCellDef="let row; let i = index">
                                    <input [formControl]="getControl(i, 'name')" />
                                </ng-container>
                            </ng-container>

                            <ng-container avaColumnDef="age">
                                <ng-container *avaHeaderCellDef> Age </ng-container>
                                <ng-container *avaCellDef="let row; let i = index">
                                    <input [formControl]="getControl(i, 'age')" />
                                </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="email">
                                <ng-container *avaHeaderCellDef> Email </ng-container>
                                <ng-container *avaCellDef="let row; let i = index">
                                    <input [formControl]="getControl(i, 'email')" />
                                </ng-container>
                            </ng-container>
                            <ng-container avaColumnDef="country">
                                <ng-container *avaHeaderCellDef> Country </ng-container>
                                <ng-container *avaCellDef="let row; let i = index">
                                    <input [formControl]="getControl(i, 'country')" />
                                </ng-container>
                            </ng-container>
                        </ava-data-grid>


                        <pre>{{combinedFormValue | json}}</pre>
                        <ava-button label="Submit" variant="primary" (userClick)="getFormData()"></ava-button>

                    </div>
                </div>
            </div>
        </div>
    </div>