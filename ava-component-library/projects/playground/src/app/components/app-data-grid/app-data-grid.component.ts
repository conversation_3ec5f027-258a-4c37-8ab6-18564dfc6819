import { Component } from '@angular/core';
import { DataGridComponent } from '../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonComponent, IconComponent } from '../../../../../play-comp-library/src/public-api';
import { CommonModule } from '@angular/common';
import { AvaColumnDefDirective } from '../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';

@Component({
  selector: 'app-app-data-grid',
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IconComponent, ButtonComponent,
    DataGridComponent, AvaColumnDefDirective, AvaHeaderCellDefDirective, AvaCellDefDirective],
  templateUrl: './app-data-grid.component.html',
  styleUrl: './app-data-grid.component.scss'
})
export class AppDataGridComponent {
  formGroups: FormGroup[] = [];
  column = ['name', 'age', 'actions']
  dataSource = [
    { id: 1, name: 'Alice Johnson', age: 28, email: '<EMAIL>', status: 'Active', country: 'USA' },
    { id: 2, name: 'Bob Smith', age: 34, email: '<EMAIL>', status: 'Inactive', country: 'Canada' },
    { id: 3, name: 'Carlos Martinez', age: 25, email: '<EMAIL>', status: 'Pending', country: 'Mexico' },
    { id: 4, name: 'Diana Lee', age: 42, email: '<EMAIL>', status: 'Active', country: 'UK' },
    { id: 5, name: 'Ethan Brown', age: 31, email: '<EMAIL>', status: 'Inactive', country: 'Germany' },
    { id: 6, name: 'Fiona Green', age: 29, email: '<EMAIL>', status: 'Pending', country: 'Australia' },
    { id: 7, name: 'George Wang', age: 37, email: '<EMAIL>', status: 'Active', country: 'China' },
    { id: 8, name: 'Hannah Kim', age: 26, email: '<EMAIL>', status: 'Inactive', country: 'South Korea' },
    { id: 9, name: 'Ian Davis', age: 33, email: '<EMAIL>', status: 'Pending', country: 'India' },
    { id: 10, name: 'Julia Roberts', age: 45, email: '<EMAIL>', status: 'Active', country: 'USA' },
  ];
  constructor(private fb: FormBuilder) { }
  ngOnInit() {
    this.createFormGroups(this.dataSource);
  }
  basic() {
  }
  createFormGroups(data: any[]) {
    this.formGroups = data.map(row =>
      this.fb.group({
        id: [row.id],
        name: [row.name],
        age: [row.age],
        email: [row.email],
        status: [row.status],
        country: [row.country]
      })
    );
  }

  getFormData() {
    console.log("formGroups", this.formGroups);
  }

  get combinedFormValue(): any[] {
    return this.formGroups.map(group => group.value);
  }

  onDataSorted(sortedData: any[]) {
    this.dataSource = sortedData;
    this.createFormGroups(sortedData);
  }
  getControl(i: number, controlName: string): FormControl {
    return this.formGroups[i].get(controlName) as FormControl;
  }
  edit(row: any) {

  }
  delete(row: any) {

  }
}
